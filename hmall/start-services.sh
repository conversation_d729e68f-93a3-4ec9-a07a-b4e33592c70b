#!/bin/bash

# HMall 微服务启动脚本
# 用于启动所有微服务进行测试和开发

echo "🚀 开始启动 HMall 微服务集群..."

# 检查Java环境
if ! command -v java &> /dev/null; then
    echo "❌ 错误: 未找到Java环境，请先安装Java 11或更高版本"
    exit 1
fi

# 检查Maven环境
if ! command -v mvn &> /dev/null; then
    echo "❌ 错误: 未找到Maven环境，请先安装Maven"
    exit 1
fi

# 设置工作目录
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd "$SCRIPT_DIR"

echo "📁 当前工作目录: $SCRIPT_DIR"

# 检查必要的模块是否存在
REQUIRED_MODULES=("hm-common" "hm-user-service" "hm-product-service" "hm-cart-service" "hm-trade-service" "hm-pay-service")
for module in "${REQUIRED_MODULES[@]}"; do
    if [ ! -d "$module" ]; then
        echo "❌ 错误: 缺少必要模块 $module"
        exit 1
    fi
done

# 编译所有模块
echo "🔨 正在编译项目..."
mvn clean compile -DskipTests

if [ $? -ne 0 ]; then
    echo "❌ 错误: 项目编译失败"
    exit 1
fi

echo "✅ 编译完成"

# 创建日志目录
mkdir -p logs

# 启动函数
start_service() {
    local service_name=$1
    local service_dir=$2
    local port=$3
    local log_file=$4
    local pid_file=$5
    local wait_time=${6:-15}

    echo "🔄 启动 $service_name - 端口: $port"

    if [ ! -d "$service_dir" ]; then
        echo "⚠️  警告: $service_dir 目录不存在，跳过启动"
        return 1
    fi

    cd "$service_dir"
    nohup mvn spring-boot:run -Dspring-boot.run.profiles=local > "../logs/$log_file" 2>&1 &
    local pid=$!
    echo "$pid" > "../logs/$pid_file"
    echo "   PID: $pid"
    cd ..

    echo "   ⏳ 等待 $wait_time 秒..."
    sleep $wait_time

    return 0
}

# 按依赖顺序启动服务

# 1. 启动用户服务 (基础服务)
start_service "用户服务 (hm-user-service)" "hm-user-service" "8083" "user-service.log" "user-service.pid" 15

# 2. 启动商品服务 (基础服务)
start_service "商品管理服务 (hm-product-service)" "hm-product-service" "8081" "product-service.log" "product-service.pid" 15

# 3. 启动购物车服务 (依赖商品服务)
start_service "购物车服务 (hm-cart-service)" "hm-cart-service" "8082" "cart-service.log" "cart-service.pid" 15

# 4. 启动交易服务 (依赖用户、商品、购物车服务)
start_service "交易服务 (hm-trade-service)" "hm-trade-service" "8084" "trade-service.log" "trade-service.pid" 15

# 5. 启动支付服务 (依赖交易服务)
start_service "支付服务 (hm-pay-service)" "hm-pay-service" "8085" "pay-service.log" "pay-service.pid" 15

# 6. 启动主服务 (遗留服务，用于兼容)
if [ -d "hm-service" ]; then
    echo "🔄 启动主服务 (hm-service) [遗留服务] - 端口: 8080"
    cd hm-service
    nohup mvn spring-boot:run -Dspring-boot.run.profiles=local > ../logs/main-service.log 2>&1 &
    MAIN_PID=$!
    echo "$MAIN_PID" > ../logs/main-service.pid
    echo "   PID: $MAIN_PID"
    cd ..
    echo "   ⏳ 等待 15 秒..."
    sleep 15
fi

echo ""
echo "所有服务启动完成！"
echo "服务信息:"
echo "- 商品管理服务: http://localhost:8081 (PID: $PRODUCT_PID)"
echo "- 购物车服务: http://localhost:8082 (PID: $CART_PID)"
echo "- 主服务: http://localhost:8080 (PID: $MAIN_PID)"
echo ""
echo "日志文件位置:"
echo "- 商品服务日志: logs/product-service.log"
echo "- 购物车服务日志: logs/cart-service.log"
echo "- 主服务日志: logs/main-service.log"
echo ""
echo "停止所有服务请运行: ./stop-services.sh"
echo ""
echo "等待服务完全启动 (约30秒)..."
sleep 30

# 检查服务健康状态
echo "检查服务健康状态..."

# 检查商品服务
if curl -s http://localhost:8081/actuator/health > /dev/null 2>&1; then
    echo "✓ 商品管理服务 (8081) - 运行正常"
else
    echo "✗ 商品管理服务 (8081) - 可能未正常启动"
fi

# 检查购物车服务
if curl -s http://localhost:8082/actuator/health > /dev/null 2>&1; then
    echo "✓ 购物车服务 (8082) - 运行正常"
else
    echo "✗ 购物车服务 (8082) - 可能未正常启动"
fi

# 检查主服务
if curl -s http://localhost:8080/actuator/health > /dev/null 2>&1; then
    echo "✓ 主服务 (8080) - 运行正常"
else
    echo "✗ 主服务 (8080) - 可能未正常启动"
fi

echo ""
echo "微服务启动脚本执行完成！"
echo "如果服务未正常启动，请查看对应的日志文件进行排查。"
